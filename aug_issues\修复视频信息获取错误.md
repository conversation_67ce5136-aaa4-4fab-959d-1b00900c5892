# 修复视频信息获取错误

## 问题描述
获取视频信息时会报错：响应格式错误：[object Object]

## 问题分析
- 前端在处理响应时，可能收到的是对象而不是字符串
- JSON.parse() 对已经是对象的数据会失败
- 错误处理显示"[object Object]"不够友好

## 解决方案
改进前端错误处理和响应检查：
1. 添加响应类型检查
2. 改进错误处理逻辑
3. 添加调试日志
4. 优化错误信息显示

## 修改文件
- dy-workers.js (前端getVideoInfo函数)

## 执行状态
- [x] 计划制定
- [x] 代码修改
- [ ] 用户验证

## 修改详情
已修改 dy-workers.js 文件中的 getVideoInfo 函数（第504-611行）：

### 主要改进：
1. **智能响应处理**：
   - 添加响应类型检查（string vs object）
   - 如果是字符串则解析JSON，如果已是对象则直接使用
   - 避免对已经是对象的数据进行JSON.parse()

2. **增强调试功能**：
   - 添加console.log输出响应内容和类型
   - 记录解析后的视频信息
   - 便于问题排查

3. **改进错误处理**：
   - 详细的错误信息显示
   - 避免显示"[object Object]"
   - 截取响应内容前100字符用于错误提示
   - 添加错误类型判断

4. **数据验证**：
   - 验证videoInfo对象的有效性
   - 确保必要字段存在

### 预期效果：
- 不再出现"响应格式错误：[object Object]"
- 提供更清晰的错误信息
- 增强容错能力
